const express = require('express');
const http = require('http');

const app = express();
const server = http.createServer(app);

// Simple health check
app.get('/', (req, res) => {
  res.json({
    status: 'OK',
    message: 'WebRTC Signaling Server Test',
    timestamp: new Date().toISOString(),
    port: process.env.PORT || 3000,
    environment: process.env.NODE_ENV || 'development'
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  });
});

// For Hostinger, use the PORT environment variable
const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || '0.0.0.0';

server.listen(PORT, HOST, () => {
  console.log(`Test server running on ${HOST}:${PORT}`);
  console.log('Environment:', process.env.NODE_ENV || 'development');
  console.log('Available endpoints:');
  console.log('- Root: /');
  console.log('- Health: /health');
});

module.exports = { app, server };
