<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Process\Process as SymfonyProcess;

class VideoCallServerController extends Controller
{
    private $pidFile = 'video_call_server.pid';
    private $logFile = 'video_call_server.log';
    private $serverPath = 'webrtc-signaling-server';
    private $serverPort;
    private $serverDomain;

    public function __construct()
    {
        // For Hostinger deployment, use the actual domain
        $this->serverPort = env('VIDEO_CALL_SERVER_PORT', 443); // HTTPS port
        $this->serverDomain = env('VIDEO_CALL_SERVER_DOMAIN', 'martserver.eurofitqatar.com');
    }

    public function index()
    {
        $status = $this->getServerStatus();
        $logs = $this->getServerLogs();
        
        return view('app.settings.video_call_server', compact('status', 'logs'));
    }

    public function start(Request $request)
    {
        try {
            if ($this->isServerRunning()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Video call server is already running'
                ]);
            }

            $result = $this->startServer();
            
            if ($result['success']) {
                Log::info('Video call server started successfully');
                return response()->json([
                    'success' => true,
                    'message' => 'Video call server started successfully',
                    'pid' => $result['pid']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to start video call server: ' . $result['error']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error starting video call server: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error starting server: ' . $e->getMessage()
            ]);
        }
    }

    public function stop(Request $request)
    {
        try {
            if (!$this->isServerRunning()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Video call server is not running'
                ]);
            }

            $result = $this->stopServer();
            
            if ($result['success']) {
                Log::info('Video call server stopped successfully');
                return response()->json([
                    'success' => true,
                    'message' => 'Video call server stopped successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to stop video call server: ' . $result['error']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error stopping video call server: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error stopping server: ' . $e->getMessage()
            ]);
        }
    }

    public function status(Request $request)
    {
        $status = $this->getServerStatus();
        return response()->json($status);
    }

    public function logs(Request $request)
    {
        $logs = $this->getServerLogs();
        return response()->json(['logs' => $logs]);
    }

    public function testPorts(Request $request)
    {
        $availablePorts = $this->testPortAvailability();
        $recommendedPort = $this->findAvailablePort();

        return response()->json([
            'available_ports' => $availablePorts,
            'recommended_port' => $recommendedPort,
            'current_port' => $this->serverPort,
            'test_results' => array_map(function($port) {
                return [
                    'port' => $port,
                    'available' => !$this->isPortOpen($port),
                    'status' => !$this->isPortOpen($port) ? 'Available' : 'In Use'
                ];
            }, [8080, 8443, 8000, 3000, 3001, 8888, 9000])
        ]);
    }

    private function getServerStatus()
    {
        $isRunning = $this->isServerRunning();
        $pid = $this->getServerPid();

        // Get the actual port being used
        $actualPort = $this->getActualServerPort();
        $portOpen = $this->isPortOpen($actualPort);

        return [
            'running' => $isRunning,
            'pid' => $pid,
            'port' => $actualPort,
            'port_open' => $portOpen,
            'server_path' => $this->serverPath,
            'last_checked' => now()->toDateTimeString(),
            'available_ports' => $this->testPortAvailability()
        ];
    }

    private function getActualServerPort()
    {
        // Check if we have a stored port from when server was started
        if (Storage::exists('video_call_server_port.txt')) {
            return (int) Storage::get('video_call_server_port.txt');
        }

        return $this->serverPort;
    }

    private function isServerRunning()
    {
        $pid = $this->getServerPid();
        
        if (!$pid) {
            return false;
        }

        // Check if process is actually running
        if (PHP_OS_FAMILY === 'Windows') {
            $command = "tasklist /FI \"PID eq $pid\" 2>NUL | find \"$pid\" >NUL";
            exec($command, $output, $returnCode);
            return $returnCode === 0;
        } else {
            return file_exists("/proc/$pid");
        }
    }

    private function getServerPid()
    {
        if (Storage::exists($this->pidFile)) {
            return (int) Storage::get($this->pidFile);
        }
        return null;
    }

    private function isPortOpen($port)
    {
        // For Hostinger deployment, test the actual domain
        if (isset($this->serverDomain)) {
            return $this->testDomainHealth();
        }

        $connection = @fsockopen('localhost', $port, $errno, $errstr, 1);
        if ($connection) {
            fclose($connection);
            return true;
        }
        return false;
    }

    private function testDomainHealth()
    {
        try {
            $url = "https://{$this->serverDomain}/health";
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'method' => 'GET'
                ]
            ]);

            $response = @file_get_contents($url, false, $context);
            return $response !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    private function findAvailablePort()
    {
        // Common ports that work on shared hosting
        $commonPorts = [8080, 8443, 8000, 3000, 8888, 9000, 8081, 8082];

        foreach ($commonPorts as $port) {
            if (!$this->isPortOpen($port)) {
                return $port;
            }
        }

        // If none of the common ports are available, return the configured port
        return $this->serverPort;
    }

    private function testPortAvailability()
    {
        $availablePorts = [];
        $testPorts = [8080, 8443, 8000, 3000, 3001, 8888, 9000, 8081, 8082];

        foreach ($testPorts as $port) {
            if (!$this->isPortOpen($port)) {
                $availablePorts[] = $port;
            }
        }

        return $availablePorts;
    }

    private function startServer()
    {
        $serverFullPath = base_path($this->serverPath);

        if (!file_exists($serverFullPath . '/server.js')) {
            return [
                'success' => false,
                'error' => 'Server file not found at: ' . $serverFullPath . '/server.js'
            ];
        }

        // Find an available port
        $availablePort = $this->findAvailablePort();
        $this->serverPort = $availablePort;

        try {
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows command with port parameter
                $command = "cd /d \"$serverFullPath\" && start /B node server.js $availablePort > " . storage_path('logs/' . $this->logFile) . " 2>&1 & echo %ERRORLEVEL%";
                $output = shell_exec($command);

                // Get the PID by finding the node process
                $pidCommand = 'wmic process where "name=\'node.exe\' and commandline like \'%server.js%\'" get processid /value';
                $pidOutput = shell_exec($pidCommand);

                if (preg_match('/ProcessId=(\d+)/', $pidOutput, $matches)) {
                    $pid = $matches[1];
                    Storage::put($this->pidFile, $pid);
                    Storage::put('video_call_server_port.txt', $availablePort);
                    return ['success' => true, 'pid' => $pid, 'port' => $availablePort];
                }
            } else {
                // Linux/Unix command with port parameter
                $logPath = storage_path('logs/' . $this->logFile);
                $command = "cd \"$serverFullPath\" && nohup node server.js $availablePort > \"$logPath\" 2>&1 & echo $!";
                $pid = trim(shell_exec($command));

                if ($pid && is_numeric($pid)) {
                    Storage::put($this->pidFile, $pid);
                    Storage::put('video_call_server_port.txt', $availablePort);
                    return ['success' => true, 'pid' => $pid, 'port' => $availablePort];
                }
            }

            return [
                'success' => false,
                'error' => 'Failed to start server process'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    private function stopServer()
    {
        $pid = $this->getServerPid();

        if (!$pid) {
            return [
                'success' => false,
                'error' => 'No PID found'
            ];
        }

        try {
            if (PHP_OS_FAMILY === 'Windows') {
                $command = "taskkill /F /PID $pid";
            } else {
                $command = "kill -9 $pid";
            }

            exec($command, $output, $returnCode);

            // Remove PID file
            if (Storage::exists($this->pidFile)) {
                Storage::delete($this->pidFile);
            }

            return [
                'success' => $returnCode === 0,
                'error' => $returnCode !== 0 ? 'Failed to kill process' : null
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    private function getServerLogs()
    {
        $logPath = storage_path('logs/' . $this->logFile);

        if (file_exists($logPath)) {
            $logs = file_get_contents($logPath);
            // Get last 50 lines
            $lines = explode("\n", $logs);
            $lastLines = array_slice($lines, -50);
            return implode("\n", $lastLines);
        }

        return 'No logs available';
    }
}
