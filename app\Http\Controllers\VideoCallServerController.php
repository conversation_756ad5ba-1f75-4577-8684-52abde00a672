<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Process\Process as SymfonyProcess;

class VideoCallServerController extends Controller
{
    private $pidFile = 'video_call_server.pid';
    private $logFile = 'video_call_server.log';
    private $serverPath = 'webrtc-signaling-server';
    private $serverPort = 3001;

    public function index()
    {
        $status = $this->getServerStatus();
        $logs = $this->getServerLogs();
        
        return view('app.settings.video_call_server', compact('status', 'logs'));
    }

    public function start(Request $request)
    {
        try {
            if ($this->isServerRunning()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Video call server is already running'
                ]);
            }

            $result = $this->startServer();
            
            if ($result['success']) {
                Log::info('Video call server started successfully');
                return response()->json([
                    'success' => true,
                    'message' => 'Video call server started successfully',
                    'pid' => $result['pid']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to start video call server: ' . $result['error']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error starting video call server: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error starting server: ' . $e->getMessage()
            ]);
        }
    }

    public function stop(Request $request)
    {
        try {
            if (!$this->isServerRunning()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Video call server is not running'
                ]);
            }

            $result = $this->stopServer();
            
            if ($result['success']) {
                Log::info('Video call server stopped successfully');
                return response()->json([
                    'success' => true,
                    'message' => 'Video call server stopped successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to stop video call server: ' . $result['error']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error stopping video call server: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error stopping server: ' . $e->getMessage()
            ]);
        }
    }

    public function status(Request $request)
    {
        $status = $this->getServerStatus();
        return response()->json($status);
    }

    public function logs(Request $request)
    {
        $logs = $this->getServerLogs();
        return response()->json(['logs' => $logs]);
    }

    private function getServerStatus()
    {
        $isRunning = $this->isServerRunning();
        $pid = $this->getServerPid();
        $portOpen = $this->isPortOpen($this->serverPort);
        
        return [
            'running' => $isRunning,
            'pid' => $pid,
            'port' => $this->serverPort,
            'port_open' => $portOpen,
            'server_path' => $this->serverPath,
            'last_checked' => now()->toDateTimeString()
        ];
    }

    private function isServerRunning()
    {
        $pid = $this->getServerPid();
        
        if (!$pid) {
            return false;
        }

        // Check if process is actually running
        if (PHP_OS_FAMILY === 'Windows') {
            $command = "tasklist /FI \"PID eq $pid\" 2>NUL | find \"$pid\" >NUL";
            exec($command, $output, $returnCode);
            return $returnCode === 0;
        } else {
            return file_exists("/proc/$pid");
        }
    }

    private function getServerPid()
    {
        if (Storage::exists($this->pidFile)) {
            return (int) Storage::get($this->pidFile);
        }
        return null;
    }

    private function isPortOpen($port)
    {
        $connection = @fsockopen('localhost', $port, $errno, $errstr, 1);
        if ($connection) {
            fclose($connection);
            return true;
        }
        return false;
    }

    private function startServer()
    {
        $serverFullPath = base_path($this->serverPath);

        if (!file_exists($serverFullPath . '/server.js')) {
            return [
                'success' => false,
                'error' => 'Server file not found at: ' . $serverFullPath . '/server.js'
            ];
        }

        try {
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows command
                $command = "cd /d \"$serverFullPath\" && start /B node server.js > " . storage_path('logs/' . $this->logFile) . " 2>&1 & echo %ERRORLEVEL%";
                $output = shell_exec($command);

                // Get the PID by finding the node process
                $pidCommand = 'wmic process where "name=\'node.exe\' and commandline like \'%server.js%\'" get processid /value';
                $pidOutput = shell_exec($pidCommand);

                if (preg_match('/ProcessId=(\d+)/', $pidOutput, $matches)) {
                    $pid = $matches[1];
                    Storage::put($this->pidFile, $pid);
                    return ['success' => true, 'pid' => $pid];
                }
            } else {
                // Linux/Unix command
                $logPath = storage_path('logs/' . $this->logFile);
                $command = "cd \"$serverFullPath\" && nohup node server.js > \"$logPath\" 2>&1 & echo $!";
                $pid = trim(shell_exec($command));

                if ($pid && is_numeric($pid)) {
                    Storage::put($this->pidFile, $pid);
                    return ['success' => true, 'pid' => $pid];
                }
            }

            return [
                'success' => false,
                'error' => 'Failed to start server process'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    private function stopServer()
    {
        $pid = $this->getServerPid();

        if (!$pid) {
            return [
                'success' => false,
                'error' => 'No PID found'
            ];
        }

        try {
            if (PHP_OS_FAMILY === 'Windows') {
                $command = "taskkill /F /PID $pid";
            } else {
                $command = "kill -9 $pid";
            }

            exec($command, $output, $returnCode);

            // Remove PID file
            if (Storage::exists($this->pidFile)) {
                Storage::delete($this->pidFile);
            }

            return [
                'success' => $returnCode === 0,
                'error' => $returnCode !== 0 ? 'Failed to kill process' : null
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    private function getServerLogs()
    {
        $logPath = storage_path('logs/' . $this->logFile);

        if (file_exists($logPath)) {
            $logs = file_get_contents($logPath);
            // Get last 50 lines
            $lines = explode("\n", $logs);
            $lastLines = array_slice($lines, -50);
            return implode("\n", $lastLines);
        }

        return 'No logs available';
    }
}
