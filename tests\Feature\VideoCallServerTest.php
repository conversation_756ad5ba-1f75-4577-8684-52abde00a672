<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

class VideoCallServerTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user
        $this->admin = User::factory()->create([
            'is_admin' => true,
            'role' => 'admin'
        ]);
    }

    /** @test */
    public function admin_can_access_video_call_server_management_page()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('video-call-server.index'));

        $response->assertStatus(200);
        $response->assertViewIs('app.settings.video_call_server');
        $response->assertViewHas(['status', 'logs']);
    }

    /** @test */
    public function non_admin_cannot_access_video_call_server_management_page()
    {
        $user = User::factory()->create(['is_admin' => false]);

        $response = $this->actingAs($user)
            ->get(route('video-call-server.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function can_get_server_status()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('video-call-server.status'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'running',
            'pid',
            'port',
            'port_open',
            'server_path',
            'last_checked'
        ]);
    }

    /** @test */
    public function can_get_server_logs()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('video-call-server.logs'));

        $response->assertStatus(200);
        $response->assertJsonStructure(['logs']);
    }

    /** @test */
    public function start_server_returns_appropriate_response()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('video-call-server.start'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message'
        ]);
    }

    /** @test */
    public function stop_server_returns_appropriate_response()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('video-call-server.stop'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message'
        ]);
    }

    /** @test */
    public function routes_are_protected_by_auth_middleware()
    {
        // Test without authentication
        $this->get(route('video-call-server.index'))
            ->assertRedirect(route('login'));

        $this->post(route('video-call-server.start'))
            ->assertRedirect(route('login'));

        $this->post(route('video-call-server.stop'))
            ->assertRedirect(route('login'));

        $this->get(route('video-call-server.status'))
            ->assertRedirect(route('login'));

        $this->get(route('video-call-server.logs'))
            ->assertRedirect(route('login'));
    }

    protected function tearDown(): void
    {
        // Clean up any test files
        Storage::delete('video_call_server.pid');
        parent::tearDown();
    }
}
